from rest_framework import serializers
from appointments.models import DoctorAvailability
from appointments.constants import APPOINTMENT_MODE_CHOICES

class DoctorAvailabilitySerializer(serializers.ModelSerializer):
    # NEW: Add custom_url_username field instead of exposing internal doctor ID
    doctor_custom_url = serializers.Char<PERSON>ield(source='doctor.custom_url_username', read_only=True)
    doctor_name = serializers.CharField(source='doctor.get_full_name', read_only=True)
    
    class Meta:
        model = DoctorAvailability
        fields = [
            'id', 'doctor_custom_url', 'doctor_name', 'clinic', 'enterprise', 'title',
            'start_date', 'end_date', 'start_time', 'end_time',
            'is_active', 'recurrence_type', 'recurrence_interval',
            'recurrence_days', 'recurrence_month_day',
            'recurrence_end_date', 'recurrence_count', 'mode', 'need_payment'
        ]
        read_only_fields = ['id', 'doctor_custom_url', 'doctor_name']
        
    def to_representation(self, instance):
        data = super().to_representation(instance)
        # Ensure we don't expose internal doctor ID
        data.pop('doctor', None)  # Remove if exists
        return data

    def validate(self, data):
        """
        Custom validation for availability data
        """
        # Validate mode
        mode = data.get('mode', 'in_person')
        if isinstance(mode, list):
            mode = ','.join(mode)
        elif not isinstance(mode, str):
            raise serializers.ValidationError({
                'mode': 'Mode must be a string or list of appointment modes'
            })
        
        # Split and validate each mode
        modes = [m.strip() for m in mode.split(',')]
        valid_modes = dict(APPOINTMENT_MODE_CHOICES).keys()
        
        for m in modes:
            if m not in valid_modes:
                raise serializers.ValidationError({
                    'mode': f'Invalid mode: {m}. Must be one of: {", ".join(valid_modes)}'
                })
        
        if not modes:  # Ensure at least one mode is specified
            raise serializers.ValidationError({
                'mode': 'At least one appointment mode must be specified'
            })
            
        data['mode'] = mode  # Store as comma-separated string

        # Validate recurrence settings
        recurrence_type = data.get('recurrence_type', 'none')
        
        # Validate basic required fields for all types
        if not data.get('start_date'):
            raise serializers.ValidationError({
                'start_date': 'Start date is required for all availability types.'
            })
            
        if not data.get('start_time') or not data.get('end_time'):
            raise serializers.ValidationError({
                'start_time': 'Start time and end time are required for all availability types.',
                'end_time': 'Start time and end time are required for all availability types.'
            })
        
        # Always set end_date equal to start_date since they represent the same day
        data['end_date'] = data['start_date']
        
        # Validate recurrence-specific fields
        if recurrence_type != 'none':
            # Validate recurrence interval
            recurrence_interval = data.get('recurrence_interval', 1)
            if recurrence_interval < 1:
                raise serializers.ValidationError({
                    'recurrence_interval': 'Recurrence interval must be a positive integer.'
                })
                
            # Weekly recurrence requires recurrence_days
            if recurrence_type == 'weekly':
                if not data.get('recurrence_days'):
                    raise serializers.ValidationError({
                        'recurrence_days': 'Weekly recurrence requires recurrence_days to be set.'
                    })
                
                # Validate recurrence_days format
                recurrence_days = data.get('recurrence_days')
                valid_days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                
                try:
                    # Try to parse as JSON first
                    import json
                    days = json.loads(recurrence_days)
                    if isinstance(days, list):
                        for day in days:
                            if day.lower() not in valid_days:
                                raise serializers.ValidationError({
                                    'recurrence_days': f'Invalid day: {day}. Valid days are: {", ".join(valid_days)}'
                                })
                    else:
                        raise serializers.ValidationError({
                            'recurrence_days': 'recurrence_days must be a JSON array of days.'
                        })
                except (json.JSONDecodeError, TypeError):
                    # Fall back to string parsing
                    for day in recurrence_days.split(','):
                        day = day.strip().lower()
                        if day not in valid_days:
                            raise serializers.ValidationError({
                                'recurrence_days': f'Invalid day: {day}. Valid days are: {", ".join(valid_days)}'
                            })
                
            # Monthly recurrence requires recurrence_month_day
            if recurrence_type == 'monthly':
                recurrence_month_day = data.get('recurrence_month_day')
                if not recurrence_month_day:
                    raise serializers.ValidationError({
                        'recurrence_month_day': 'Monthly recurrence requires recurrence_month_day to be set.'
                    })
                
                if not (1 <= recurrence_month_day <= 31):
                    raise serializers.ValidationError({
                        'recurrence_month_day': 'Monthly recurrence day must be between 1 and 31.'
                    })
                
            # Recurrence must have either an end date or count
            if not data.get('recurrence_end_date') and not data.get('recurrence_count'):
                raise serializers.ValidationError({
                    'recurrence_end_date': 'Recurrence must have either an end date or count.',
                    'recurrence_count': 'Recurrence must have either an end date or count.'
                })
                
            # Validate recurrence count
            if data.get('recurrence_count') and data.get('recurrence_count') < 1:
                raise serializers.ValidationError({
                    'recurrence_count': 'Recurrence count must be a positive integer.'
                })
                
        # Validate recurrence end date
        recurrence_end_date = data.get('recurrence_end_date')
        if recurrence_end_date and data['start_date'] and recurrence_end_date < data['start_date']:
            raise serializers.ValidationError({
                'recurrence_end_date': 'Recurrence end date must be after start date.'
            })
            
        # Validate time ranges
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        
        if start_time and end_time and start_time >= end_time:
            raise serializers.ValidationError({
                'end_time': 'End time must be after start time.'
            })
            
        return data

    def create(self, validated_data):
        availability = DoctorAvailability.objects.create(**validated_data)
        return availability

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance 